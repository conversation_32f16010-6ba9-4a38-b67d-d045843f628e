# 商家端微信绑定功能

## 功能概述

商家端微信绑定功能允许商家通过扫码方式绑定微信公众号和小程序，用于接收订单付款等重要通知。

## 功能特性

- ✅ 支持微信公众号绑定
- ✅ 支持微信小程序绑定
- ✅ 二维码扫码绑定流程
- ✅ 实时状态轮询
- ✅ 绑定状态管理
- ✅ 通知开关控制
- ✅ 解绑操作
- ✅ 响应式设计
- ✅ 友好的用户界面

## 文件结构

```
seller/src/views/shop/
├── WechatBind.vue          # 微信绑定主页面
├── WechatBindPanel.vue     # 微信绑定面板组件
├── WechatBindTest.vue      # 功能测试页面
└── WECHAT_BIND_README.md   # 说明文档

seller/src/api/shops.js     # API接口定义
seller/src/styles/wechat-bind.scss  # 样式文件
```

## API接口

### 1. 生成微信公众号绑定二维码
```javascript
GET /store/seller/store/wechat-bind/qrcode/official-account
```

### 2. 生成微信小程序绑定二维码
```javascript
GET /store/seller/store/wechat-bind/qrcode/mini-program
```

### 3. 检查绑定状态
```javascript
GET /store/seller/store/wechat-bind/status?bindToken={token}&unionType={type}
```

### 4. 获取商家绑定状态
```javascript
GET /store/seller/store/wechat-bind/bind-status
```

### 5. 更新通知状态
```javascript
PUT /store/seller/store/connect/notification?unionType={type}&enable={true/false}
```

### 6. 解除绑定
```javascript
DELETE /store/seller/store/wechat-bind/unbind?unionType={type}
```

## 组件使用

### WechatBindPanel 组件

```vue
<template>
  <WechatBindPanel
    :union-type="unionType"
    :bind-info="bindInfo"
    @bind-success="handleBindSuccess"
    @unbind-success="handleUnbindSuccess"
  />
</template>

<script>
import WechatBindPanel from './WechatBindPanel.vue'

export default {
  components: {
    WechatBindPanel
  },
  data() {
    return {
      unionType: 'WECHAT_OFFIACCOUNT_OPEN_ID', // 或 'WECHAT_MP_OPEN_ID'
      bindInfo: null // 绑定信息对象
    }
  },
  methods: {
    handleBindSuccess(unionType) {
      console.log('绑定成功:', unionType)
    },
    handleUnbindSuccess(unionType) {
      console.log('解绑成功:', unionType)
    }
  }
}
</script>
```

### Props

- `unionType`: String, 必需 - 绑定类型 ('WECHAT_OFFIACCOUNT_OPEN_ID' | 'WECHAT_MP_OPEN_ID')
- `bindInfo`: Object, 可选 - 绑定信息对象

### Events

- `bind-success`: 绑定成功事件
- `unbind-success`: 解绑成功事件

## 绑定流程

1. **未绑定状态**: 显示"开始绑定"按钮
2. **点击开始绑定**: 调用API生成二维码
3. **显示二维码**: 用户使用微信扫码
4. **状态轮询**: 每2秒检查一次绑定状态
5. **绑定成功**: 显示绑定成功界面，提供通知开关和解绑按钮

## 状态说明

- `WAITING`: 等待扫码
- `SCANNED`: 已扫码，等待确认
- `SUCCESS`: 绑定成功
- `EXPIRED`: 二维码过期
- `FAILED`: 绑定失败

## 样式定制

样式文件位于 `seller/src/styles/wechat-bind.scss`，包含：

- 微信品牌色定义
- 响应式设计
- 动画效果
- 状态样式

## 测试

访问测试页面进行功能验证：
```
http://localhost:10002/#/store/wechat-bind-test
```

测试页面提供：
- API接口测试
- 组件功能测试
- 模拟数据控制

## 路由配置

```javascript
{
  path: "store/wechat-bind",
  title: "微信通知绑定",
  name: "wechat-bind",
  component: () => import("@/views/shop/WechatBind.vue"),
}
```

## 注意事项

1. 确保后端API接口已实现
2. 二维码有效期为5分钟
3. 轮询间隔为2秒
4. 支持同时绑定公众号和小程序
5. 绑定后可独立控制通知开关
6. 解绑操作需要二次确认

## 错误处理

- 网络错误：显示友好提示信息
- API错误：根据错误码显示相应提示
- 超时处理：二维码过期自动停止轮询
- 状态回滚：操作失败时恢复原状态

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+
- 移动端浏览器

## 更新日志

### v1.0.0 (2024-12-20)
- 初始版本发布
- 支持微信公众号和小程序绑定
- 实现完整的绑定流程
- 添加响应式设计
- 提供测试页面
