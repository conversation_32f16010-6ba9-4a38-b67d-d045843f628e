<template>
  <div class="wechat-bind-panel">
    <!-- 未绑定状态 -->
    <div v-if="bindStatus === 'unbound'" class="unbind-section">
      <div class="status-info">
        <Icon type="ios-information-circle" size="48" color="#2d8cf0" />
        <h3>{{ unionType === 'WECHAT_OFFIACCOUNT_OPEN_ID' ? '微信公众号' : '微信小程序' }}未绑定</h3>
        <p>绑定后可接收订单付款等重要通知</p>
      </div>
      <Button type="primary" size="large" @click="startBind" :loading="loading">
        开始绑定
      </Button>
    </div>

    <!-- 绑定中状态 -->
    <div v-else-if="bindStatus === 'binding'" class="binding-section">
      <div class="qrcode-display">
        <img v-if="qrCodeUrl" :src="qrCodeUrl" alt="绑定二维码" />
        <Spin v-else size="large" />
        <p class="qr-title">请使用微信扫描二维码</p>
        <p class="qr-subtitle">扫码后在手机上确认绑定</p>
      </div>
      
      <div class="status-display">
        <Spin :spinning="polling">
          <p class="status-text">{{ statusText }}</p>
        </Spin>
      </div>
      
      <div class="action-buttons">
        <Button @click="cancelBind" :disabled="polling">取消绑定</Button>
        <Button type="primary" @click="refreshQRCode" :loading="refreshing">刷新二维码</Button>
      </div>
    </div>

    <!-- 已绑定状态 -->
    <div v-else-if="bindStatus === 'bound'" class="bound-section">
      <Result status="success" :title="`已绑定${unionType === 'WECHAT_OFFIACCOUNT_OPEN_ID' ? '微信公众号' : '微信小程序'}`">
        <template #desc>
          <p>绑定时间：{{ bindTime }}</p>
        </template>
        <template #actions>
          <div class="bound-actions">
            <div class="notification-switch">
              <i-switch 
                v-model="notificationEnabled" 
                @on-change="toggleNotification"
                :loading="notificationLoading"
              />
              <span class="switch-label">接收通知</span>
            </div>
            <Button type="error" @click="showUnbindModal">解除绑定</Button>
          </div>
        </template>
      </Result>
    </div>

    <!-- 解绑确认弹窗 -->
    <Modal
      v-model="unbindModalVisible"
      title="确认解除绑定"
      @on-ok="confirmUnbind"
      @on-cancel="unbindModalVisible = false"
      :loading="unbindLoading"
    >
      <p>确定要解除{{ unionType === 'WECHAT_OFFIACCOUNT_OPEN_ID' ? '微信公众号' : '微信小程序' }}绑定吗？</p>
      <p>解绑后将无法接收相关通知。</p>
    </Modal>
  </div>
</template>

<script>
import { 
  generateWechatOfficialAccountQRCode, 
  generateWechatMiniProgramQRCode,
  checkWechatBindStatus,
  updateWechatNotification,
  unbindWechat
} from '@/api/shops'

export default {
  name: 'WechatBindPanel',
  props: {
    unionType: {
      type: String,
      required: true,
      validator: value => ['WECHAT_OFFIACCOUNT_OPEN_ID', 'WECHAT_MP_OPEN_ID'].includes(value)
    },
    bindInfo: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      bindStatus: 'unbound', // unbound|binding|bound
      qrCodeUrl: '',
      bindToken: '',
      polling: false,
      pollTimer: null,
      notificationEnabled: true,
      statusText: '',
      loading: false,
      refreshing: false,
      notificationLoading: false,
      unbindModalVisible: false,
      unbindLoading: false,
      bindTime: ''
    }
  },
  mounted() {
    this.initBindStatus()
  },
  beforeDestroy() {
    this.stopPolling()
  },
  methods: {
    // 初始化绑定状态
    initBindStatus() {
      if (this.bindInfo) {
        this.bindStatus = 'bound'
        this.notificationEnabled = this.bindInfo.enableNotification
        this.bindTime = this.formatTime(this.bindInfo.createTime)
      } else {
        this.bindStatus = 'unbound'
      }
    },
    
    // 开始绑定
    async startBind() {
      this.loading = true
      try {
        const response = await this.generateQRCode()
        if (response.success) {
          this.qrCodeUrl = response.result.qrCodeUrl
          this.bindToken = response.result.bindToken
          this.bindStatus = 'binding'
          this.statusText = '等待扫码...'
          this.startPolling()
        } else {
          this.$Message.error('生成绑定二维码失败')
        }
      } catch (error) {
        console.error('生成绑定二维码失败:', error)
        this.$Message.error('生成绑定二维码失败')
      } finally {
        this.loading = false
      }
    },
    
    // 生成二维码
    generateQRCode() {
      if (this.unionType === 'WECHAT_OFFIACCOUNT_OPEN_ID') {
        return generateWechatOfficialAccountQRCode()
      } else {
        return generateWechatMiniProgramQRCode()
      }
    },
    
    // 开始轮询状态
    startPolling() {
      this.polling = true
      this.pollTimer = setInterval(async () => {
        try {
          const response = await checkWechatBindStatus(this.bindToken, this.unionType)
          if (response.success) {
            const status = response.result
            this.handleStatusChange(status)
          }
        } catch (error) {
          console.error('检查绑定状态失败:', error)
        }
      }, 2000) // 每2秒轮询一次
    },
    
    // 处理状态变化
    handleStatusChange(status) {
      switch (status) {
        case 'WAITING':
          this.statusText = '等待扫码...'
          break
        case 'SCANNED':
          this.statusText = '已扫码，请在手机上确认绑定'
          break
        case 'SUCCESS':
          this.statusText = '绑定成功！'
          this.onBindSuccess()
          break
        case 'EXPIRED':
          this.statusText = '二维码已过期，请重新生成'
          this.stopPolling()
          break
        case 'FAILED':
          this.statusText = '绑定失败，请重试'
          this.stopPolling()
          break
      }
    },
    
    // 绑定成功处理
    onBindSuccess() {
      this.stopPolling()
      this.bindStatus = 'bound'
      this.bindTime = new Date().toLocaleString()
      this.notificationEnabled = true
      this.$Message.success('微信绑定成功！')
      this.$emit('bind-success', this.unionType)
    },
    
    // 停止轮询
    stopPolling() {
      if (this.pollTimer) {
        clearInterval(this.pollTimer)
        this.pollTimer = null
      }
      this.polling = false
    },
    
    // 取消绑定
    cancelBind() {
      this.stopPolling()
      this.bindStatus = 'unbound'
      this.qrCodeUrl = ''
      this.bindToken = ''
      this.statusText = ''
    },
    
    // 刷新二维码
    async refreshQRCode() {
      this.refreshing = true
      this.stopPolling()
      try {
        const response = await this.generateQRCode()
        if (response.success) {
          this.qrCodeUrl = response.result.qrCodeUrl
          this.bindToken = response.result.bindToken
          this.statusText = '等待扫码...'
          this.startPolling()
        } else {
          this.$Message.error('刷新二维码失败')
        }
      } catch (error) {
        console.error('刷新二维码失败:', error)
        this.$Message.error('刷新二维码失败')
      } finally {
        this.refreshing = false
      }
    },
    
    // 切换通知状态
    async toggleNotification(enabled) {
      this.notificationLoading = true
      try {
        const response = await updateWechatNotification(this.unionType, enabled)
        if (response.success) {
          this.$Message.success(`已${enabled ? '开启' : '关闭'}通知`)
        } else {
          this.notificationEnabled = !enabled // 回滚状态
          this.$Message.error('更新通知状态失败')
        }
      } catch (error) {
        console.error('更新通知状态失败:', error)
        this.notificationEnabled = !enabled // 回滚状态
        this.$Message.error('更新通知状态失败')
      } finally {
        this.notificationLoading = false
      }
    },
    
    // 显示解绑弹窗
    showUnbindModal() {
      this.unbindModalVisible = true
    },
    
    // 确认解绑
    async confirmUnbind() {
      this.unbindLoading = true
      try {
        const response = await unbindWechat(this.unionType)
        if (response.success) {
          this.bindStatus = 'unbound'
          this.unbindModalVisible = false
          this.$Message.success('解绑成功')
          this.$emit('unbind-success', this.unionType)
        } else {
          this.$Message.error('解绑失败')
        }
      } catch (error) {
        console.error('解绑失败:', error)
        this.$Message.error('解绑失败')
      } finally {
        this.unbindLoading = false
      }
    },
    
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''
      return new Date(timeStr).toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
.wechat-bind-panel {
  padding: 24px;
  background: #fafafa;
  border-radius: 6px;
  margin: 16px 0;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.unbind-section {
  text-align: center;
  
  .status-info {
    margin-bottom: 32px;
    
    h3 {
      margin: 16px 0 8px;
      color: #333;
      font-size: 18px;
    }
    
    p {
      color: #666;
      font-size: 14px;
    }
  }
}

.binding-section {
  text-align: center;
  width: 100%;
  
  .qrcode-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 24px;
    
    img {
      width: 200px;
      height: 200px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      margin-bottom: 16px;
    }
    
    .qr-title {
      font-size: 16px;
      color: #333;
      margin: 8px 0 4px;
    }
    
    .qr-subtitle {
      font-size: 14px;
      color: #666;
      margin: 0;
    }
  }
  
  .status-display {
    margin: 24px 0;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .status-text {
      font-size: 14px;
      color: #666;
      margin: 0;
    }
  }
  
  .action-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
  }
}

.bound-section {
  width: 100%;
  
  .bound-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    margin-top: 16px;
    
    .notification-switch {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .switch-label {
        font-size: 14px;
        color: #333;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .wechat-bind-panel {
    padding: 16px;
    margin: 8px 0;
  }
  
  .binding-section {
    .qrcode-display img {
      width: 160px;
      height: 160px;
    }
    
    .action-buttons {
      flex-direction: column;
      gap: 8px;
    }
  }
  
  .bound-section .bound-actions {
    flex-direction: column;
    gap: 16px;
  }
}
</style>
