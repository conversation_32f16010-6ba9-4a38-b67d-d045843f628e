<template>
  <div class="wechat-bind-test">
    <Card>
      <div slot="title">微信绑定功能测试</div>
      
      <div class="test-section">
        <h3>API接口测试</h3>
        <div class="test-buttons">
          <Button @click="testGenerateOfficialAccountQR" :loading="loading.officialQR">
            测试生成公众号二维码
          </Button>
          <Button @click="testGenerateMiniProgramQR" :loading="loading.miniQR">
            测试生成小程序二维码
          </Button>
          <Button @click="testGetBindStatus" :loading="loading.bindStatus">
            测试获取绑定状态
          </Button>
        </div>
        
        <div class="test-results" v-if="testResults.length > 0">
          <h4>测试结果：</h4>
          <div v-for="(result, index) in testResults" :key="index" class="test-result-item">
            <Tag :color="result.success ? 'success' : 'error'">
              {{ result.success ? '成功' : '失败' }}
            </Tag>
            <span class="result-text">{{ result.message }}</span>
            <pre v-if="result.data" class="result-data">{{ JSON.stringify(result.data, null, 2) }}</pre>
          </div>
        </div>
      </div>
      
      <Divider />
      
      <div class="component-test">
        <h3>组件功能测试</h3>
        <p>下面是微信绑定组件的实际展示：</p>
        
        <Tabs v-model="activeTab" type="card">
          <TabPane label="微信公众号测试" name="official-account">
            <WechatBindPanel
              union-type="WECHAT_OFFIACCOUNT_OPEN_ID"
              :bind-info="mockOfficialAccountBindInfo"
              @bind-success="handleBindSuccess"
              @unbind-success="handleUnbindSuccess"
            />
          </TabPane>
          
          <TabPane label="微信小程序测试" name="mini-program">
            <WechatBindPanel
              union-type="WECHAT_MP_OPEN_ID"
              :bind-info="mockMiniProgramBindInfo"
              @bind-success="handleBindSuccess"
              @unbind-success="handleUnbindSuccess"
            />
          </TabPane>
        </Tabs>
        
        <div class="mock-controls">
          <h4>模拟数据控制：</h4>
          <div class="control-buttons">
            <Button @click="toggleOfficialAccountBind">
              {{ mockOfficialAccountBindInfo ? '模拟公众号解绑' : '模拟公众号已绑定' }}
            </Button>
            <Button @click="toggleMiniProgramBind">
              {{ mockMiniProgramBindInfo ? '模拟小程序解绑' : '模拟小程序已绑定' }}
            </Button>
            <Button @click="clearTestResults">清空测试结果</Button>
          </div>
        </div>
      </div>
    </Card>
  </div>
</template>

<script>
import WechatBindPanel from './WechatBindPanel.vue'
import { 
  generateWechatOfficialAccountQRCode, 
  generateWechatMiniProgramQRCode,
  getWechatBindStatus
} from '@/api/shops'

export default {
  name: 'WechatBindTest',
  components: {
    WechatBindPanel
  },
  data() {
    return {
      activeTab: 'official-account',
      loading: {
        officialQR: false,
        miniQR: false,
        bindStatus: false
      },
      testResults: [],
      mockOfficialAccountBindInfo: null,
      mockMiniProgramBindInfo: null
    }
  },
  methods: {
    // 测试生成公众号二维码
    async testGenerateOfficialAccountQR() {
      this.loading.officialQR = true
      try {
        const response = await generateWechatOfficialAccountQRCode()
        this.addTestResult(true, '生成公众号二维码成功', response)
      } catch (error) {
        this.addTestResult(false, '生成公众号二维码失败', error.response || error.message)
      } finally {
        this.loading.officialQR = false
      }
    },
    
    // 测试生成小程序二维码
    async testGenerateMiniProgramQR() {
      this.loading.miniQR = true
      try {
        const response = await generateWechatMiniProgramQRCode()
        this.addTestResult(true, '生成小程序二维码成功', response)
      } catch (error) {
        this.addTestResult(false, '生成小程序二维码失败', error.response || error.message)
      } finally {
        this.loading.miniQR = false
      }
    },
    
    // 测试获取绑定状态
    async testGetBindStatus() {
      this.loading.bindStatus = true
      try {
        const response = await getWechatBindStatus()
        this.addTestResult(true, '获取绑定状态成功', response)
      } catch (error) {
        this.addTestResult(false, '获取绑定状态失败', error.response || error.message)
      } finally {
        this.loading.bindStatus = false
      }
    },
    
    // 添加测试结果
    addTestResult(success, message, data) {
      this.testResults.unshift({
        success,
        message,
        data,
        timestamp: new Date().toLocaleString()
      })
      
      // 限制结果数量
      if (this.testResults.length > 10) {
        this.testResults = this.testResults.slice(0, 10)
      }
    },
    
    // 切换公众号绑定状态
    toggleOfficialAccountBind() {
      if (this.mockOfficialAccountBindInfo) {
        this.mockOfficialAccountBindInfo = null
      } else {
        this.mockOfficialAccountBindInfo = {
          id: 'mock-official-account-id',
          storeId: 'store001',
          storeName: '测试店铺',
          unionType: 'WECHAT_OFFIACCOUNT_OPEN_ID',
          enableNotification: true,
          createTime: new Date().toISOString()
        }
      }
    },
    
    // 切换小程序绑定状态
    toggleMiniProgramBind() {
      if (this.mockMiniProgramBindInfo) {
        this.mockMiniProgramBindInfo = null
      } else {
        this.mockMiniProgramBindInfo = {
          id: 'mock-mini-program-id',
          storeId: 'store001',
          storeName: '测试店铺',
          unionType: 'WECHAT_MP_OPEN_ID',
          enableNotification: true,
          createTime: new Date().toISOString()
        }
      }
    },
    
    // 清空测试结果
    clearTestResults() {
      this.testResults = []
    },
    
    // 处理绑定成功
    handleBindSuccess(unionType) {
      this.$Message.success(`${unionType} 绑定成功！`)
      this.addTestResult(true, `${unionType} 绑定成功`, { unionType })
    },
    
    // 处理解绑成功
    handleUnbindSuccess(unionType) {
      this.$Message.success(`${unionType} 解绑成功！`)
      this.addTestResult(true, `${unionType} 解绑成功`, { unionType })
    }
  }
}
</script>

<style lang="scss" scoped>
.wechat-bind-test {
  padding: 20px;
  
  .test-section {
    margin-bottom: 24px;
    
    h3 {
      margin-bottom: 16px;
      color: #333;
    }
    
    .test-buttons {
      display: flex;
      gap: 12px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }
    
    .test-results {
      .test-result-item {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 12px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 4px;
        
        .result-text {
          font-weight: 500;
          color: #333;
        }
        
        .result-data {
          margin-left: 16px;
          margin-top: 8px;
          padding: 8px;
          background: #fff;
          border: 1px solid #e8e8e8;
          border-radius: 4px;
          font-size: 12px;
          color: #666;
          max-height: 200px;
          overflow-y: auto;
          white-space: pre-wrap;
          word-break: break-all;
        }
      }
    }
  }
  
  .component-test {
    h3 {
      margin-bottom: 16px;
      color: #333;
    }
    
    .mock-controls {
      margin-top: 24px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 4px;
      
      h4 {
        margin-bottom: 12px;
        color: #333;
      }
      
      .control-buttons {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
      }
    }
  }
}

@media (max-width: 768px) {
  .wechat-bind-test {
    padding: 12px;
    
    .test-buttons,
    .control-buttons {
      flex-direction: column;
      
      .ivu-btn {
        width: 100%;
      }
    }
  }
}
</style>
