<template>
  <div class="wechat-bind-page">
    <Card>
      <div slot="title" class="page-header">
        <Icon type="logo-wechat" size="24" color="#07c160" />
        <span class="page-title">微信通知绑定</span>
      </div>
      
      <div class="page-content">
        <div class="description">
          <p>绑定微信公众号和小程序后，您可以及时接收订单付款、退款等重要通知。</p>
        </div>
        
        <Tabs v-model="activeTab" type="card" @on-click="handleTabChange">
          <TabPane label="微信公众号" name="official-account">
            <WechatBindPanel
              ref="officialAccountPanel"
              union-type="WECHAT_OFFIACCOUNT_OPEN_ID"
              :bind-info="officialAccountBindInfo"
              @bind-success="handleBindSuccess"
              @unbind-success="handleUnbindSuccess"
            />
          </TabPane>
          
          <TabPane label="微信小程序" name="mini-program">
            <WechatBindPanel
              ref="miniProgramPanel"
              union-type="WECHAT_MP_OPEN_ID"
              :bind-info="miniProgramBindInfo"
              @bind-success="handleBindSuccess"
              @unbind-success="handleUnbindSuccess"
            />
          </TabPane>
        </Tabs>
        
        <!-- 绑定状态概览 -->
        <div class="bind-status-overview" v-if="hasBindInfo">
          <Divider>绑定状态概览</Divider>
          <Row :gutter="16">
            <Col span="12">
              <Card class="status-card">
                <div class="status-item">
                  <Icon type="logo-wechat" size="20" color="#07c160" />
                  <span class="status-label">微信公众号</span>
                  <Tag :color="officialAccountBindInfo ? 'success' : 'default'">
                    {{ officialAccountBindInfo ? '已绑定' : '未绑定' }}
                  </Tag>
                </div>
                <div v-if="officialAccountBindInfo" class="status-detail">
                  <p>绑定时间：{{ formatTime(officialAccountBindInfo.createTime) }}</p>
                  <p>通知状态：
                    <Tag :color="officialAccountBindInfo.enableNotification ? 'success' : 'warning'">
                      {{ officialAccountBindInfo.enableNotification ? '已开启' : '已关闭' }}
                    </Tag>
                  </p>
                </div>
              </Card>
            </Col>
            
            <Col span="12">
              <Card class="status-card">
                <div class="status-item">
                  <Icon type="logo-wechat" size="20" color="#07c160" />
                  <span class="status-label">微信小程序</span>
                  <Tag :color="miniProgramBindInfo ? 'success' : 'default'">
                    {{ miniProgramBindInfo ? '已绑定' : '未绑定' }}
                  </Tag>
                </div>
                <div v-if="miniProgramBindInfo" class="status-detail">
                  <p>绑定时间：{{ formatTime(miniProgramBindInfo.createTime) }}</p>
                  <p>通知状态：
                    <Tag :color="miniProgramBindInfo.enableNotification ? 'success' : 'warning'">
                      {{ miniProgramBindInfo.enableNotification ? '已开启' : '已关闭' }}
                    </Tag>
                  </p>
                </div>
              </Card>
            </Col>
          </Row>
        </div>
      </div>
    </Card>
  </div>
</template>

<script>
import WechatBindPanel from './WechatBindPanel.vue'
import { getWechatBindStatus } from '@/api/shops'

export default {
  name: 'WechatBind',
  components: {
    WechatBindPanel
  },
  data() {
    return {
      activeTab: 'official-account',
      officialAccountBindInfo: null,
      miniProgramBindInfo: null,
      loading: false
    }
  },
  computed: {
    hasBindInfo() {
      return this.officialAccountBindInfo || this.miniProgramBindInfo
    }
  },
  mounted() {
    this.loadBindStatus()
  },
  methods: {
    // 加载绑定状态
    async loadBindStatus() {
      this.loading = true
      try {
        const response = await getWechatBindStatus()
        if (response.success && response.result) {
          const bindList = response.result
          
          // 分离公众号和小程序绑定信息
          this.officialAccountBindInfo = bindList.find(
            item => item.unionType === 'WECHAT_OFFIACCOUNT_OPEN_ID'
          ) || null
          
          this.miniProgramBindInfo = bindList.find(
            item => item.unionType === 'WECHAT_MP_OPEN_ID'
          ) || null
        }
      } catch (error) {
        console.error('加载绑定状态失败:', error)
        this.$Message.error('加载绑定状态失败')
      } finally {
        this.loading = false
      }
    },
    
    // 处理Tab切换
    handleTabChange(name) {
      this.activeTab = name
    },
    
    // 处理绑定成功
    handleBindSuccess(unionType) {
      // 重新加载绑定状态
      this.loadBindStatus()
      
      // 显示成功提示
      const typeName = unionType === 'WECHAT_OFFIACCOUNT_OPEN_ID' ? '微信公众号' : '微信小程序'
      this.$Message.success(`${typeName}绑定成功！`)
    },
    
    // 处理解绑成功
    handleUnbindSuccess(unionType) {
      // 重新加载绑定状态
      this.loadBindStatus()
      
      // 显示成功提示
      const typeName = unionType === 'WECHAT_OFFIACCOUNT_OPEN_ID' ? '微信公众号' : '微信小程序'
      this.$Message.success(`${typeName}解绑成功！`)
    },
    
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''
      return new Date(timeStr).toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
.wechat-bind-page {
  padding: 20px;
  
  .page-header {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .page-title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }
  }
  
  .page-content {
    .description {
      margin-bottom: 24px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 4px;
      border-left: 4px solid #07c160;
      
      p {
        margin: 0;
        color: #666;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
  
  .bind-status-overview {
    margin-top: 32px;
    
    .status-card {
      .status-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
        
        .status-label {
          font-weight: 500;
          color: #333;
          flex: 1;
        }
      }
      
      .status-detail {
        padding-left: 28px;
        
        p {
          margin: 4px 0;
          font-size: 13px;
          color: #666;
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .wechat-bind-page {
    padding: 12px;
    
    .bind-status-overview {
      .ivu-row {
        .ivu-col {
          margin-bottom: 16px;
        }
      }
    }
  }
}

// 自定义Tabs样式
:deep(.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab) {
  border-color: #d9d9d9;
  
  &.ivu-tabs-tab-active {
    border-color: #07c160;
    color: #07c160;
  }
}

:deep(.ivu-tabs-content) {
  padding-top: 0;
}
</style>
