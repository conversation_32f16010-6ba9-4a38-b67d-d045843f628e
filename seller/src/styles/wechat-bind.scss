// 微信绑定功能样式文件

// 微信品牌色
$wechat-green: #07c160;
$wechat-green-light: #4fc08d;
$wechat-green-dark: #059048;

// 状态颜色
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #f5222d;
$info-color: #1890ff;

// 基础颜色
$text-primary: #333;
$text-secondary: #666;
$text-disabled: #999;
$border-color: #d9d9d9;
$background-light: #fafafa;
$background-white: #fff;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 圆角
$border-radius-sm: 4px;
$border-radius-md: 6px;
$border-radius-lg: 8px;

// 阴影
$box-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
$box-shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);

// 微信绑定页面样式
.wechat-bind-page {
  .page-header {
    .page-title {
      color: $text-primary;
      font-weight: 600;
    }
  }
  
  .description {
    background: linear-gradient(135deg, rgba(7, 193, 96, 0.05) 0%, rgba(7, 193, 96, 0.02) 100%);
    border-left-color: $wechat-green;
    
    p {
      color: $text-secondary;
    }
  }
  
  // Tabs样式优化
  .ivu-tabs-card {
    .ivu-tabs-bar {
      margin-bottom: $spacing-lg;
      
      .ivu-tabs-tab {
        border-color: $border-color;
        color: $text-secondary;
        transition: all 0.3s ease;
        
        &:hover {
          color: $wechat-green;
          border-color: $wechat-green;
        }
        
        &.ivu-tabs-tab-active {
          color: $wechat-green;
          border-color: $wechat-green;
          background: rgba(7, 193, 96, 0.02);
        }
      }
    }
  }
  
  // 状态概览卡片
  .bind-status-overview {
    .status-card {
      border: 1px solid $border-color;
      border-radius: $border-radius-md;
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: $box-shadow-light;
        border-color: $wechat-green;
      }
      
      .ivu-card-body {
        padding: $spacing-lg;
      }
      
      .status-item {
        .status-label {
          font-weight: 500;
          color: $text-primary;
        }
      }
      
      .status-detail {
        p {
          color: $text-secondary;
          font-size: 13px;
          line-height: 1.6;
        }
      }
    }
  }
}

// 微信绑定面板样式
.wechat-bind-panel {
  background: $background-white;
  border: 1px solid $border-color;
  border-radius: $border-radius-md;
  box-shadow: $box-shadow-light;
  
  // 未绑定状态
  .unbind-section {
    .status-info {
      h3 {
        color: $text-primary;
        font-weight: 600;
        margin: $spacing-md 0 $spacing-sm;
      }
      
      p {
        color: $text-secondary;
        line-height: 1.6;
      }
    }
    
    .ivu-btn-primary {
      background-color: $wechat-green;
      border-color: $wechat-green;
      
      &:hover {
        background-color: $wechat-green-light;
        border-color: $wechat-green-light;
      }
      
      &:active {
        background-color: $wechat-green-dark;
        border-color: $wechat-green-dark;
      }
    }
  }
  
  // 绑定中状态
  .binding-section {
    .qrcode-display {
      img {
        border: 2px solid $border-color;
        border-radius: $border-radius-sm;
        box-shadow: $box-shadow-light;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: $wechat-green;
          box-shadow: $box-shadow-medium;
        }
      }
      
      .qr-title {
        color: $text-primary;
        font-weight: 500;
      }
      
      .qr-subtitle {
        color: $text-secondary;
      }
    }
    
    .status-display {
      .status-text {
        color: $text-secondary;
        font-size: 14px;
      }
    }
    
    .action-buttons {
      .ivu-btn {
        min-width: 100px;
        
        &.ivu-btn-primary {
          background-color: $wechat-green;
          border-color: $wechat-green;
          
          &:hover {
            background-color: $wechat-green-light;
            border-color: $wechat-green-light;
          }
        }
      }
    }
  }
  
  // 已绑定状态
  .bound-section {
    .ivu-result {
      .ivu-result-title {
        color: $text-primary;
        font-weight: 600;
      }
      
      .ivu-result-desc {
        color: $text-secondary;
      }
    }
    
    .bound-actions {
      .notification-switch {
        .switch-label {
          color: $text-primary;
          font-weight: 500;
        }
        
        .ivu-switch-checked {
          background-color: $wechat-green;
          border-color: $wechat-green;
        }
      }
      
      .ivu-btn-error {
        &:hover {
          background-color: lighten($error-color, 5%);
          border-color: lighten($error-color, 5%);
        }
      }
    }
  }
}

// 标签样式优化
.ivu-tag {
  &.ivu-tag-success {
    background-color: rgba(82, 196, 26, 0.1);
    border-color: $success-color;
    color: $success-color;
  }
  
  &.ivu-tag-warning {
    background-color: rgba(250, 173, 20, 0.1);
    border-color: $warning-color;
    color: $warning-color;
  }
  
  &.ivu-tag-default {
    background-color: rgba(153, 153, 153, 0.1);
    border-color: $text-disabled;
    color: $text-disabled;
  }
}

// 加载动画优化
.ivu-spin {
  .ivu-spin-dot {
    i {
      background-color: $wechat-green;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .wechat-bind-page {
    .bind-status-overview {
      .ivu-col {
        margin-bottom: $spacing-md;
      }
    }
  }
}

@media (max-width: 768px) {
  .wechat-bind-page {
    padding: $spacing-md;
    
    .description {
      padding: $spacing-md;
      margin-bottom: $spacing-md;
    }
    
    .bind-status-overview {
      margin-top: $spacing-lg;
      
      .status-card {
        margin-bottom: $spacing-md;
        
        .ivu-card-body {
          padding: $spacing-md;
        }
      }
    }
  }
  
  .wechat-bind-panel {
    padding: $spacing-md;
    margin: $spacing-sm 0;
    
    .binding-section {
      .qrcode-display {
        img {
          width: 160px;
          height: 160px;
        }
      }
      
      .action-buttons {
        flex-direction: column;
        gap: $spacing-sm;
        
        .ivu-btn {
          width: 100%;
        }
      }
    }
    
    .bound-section {
      .bound-actions {
        flex-direction: column;
        gap: $spacing-md;
        
        .ivu-btn {
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .wechat-bind-panel {
    .binding-section {
      .qrcode-display {
        img {
          width: 140px;
          height: 140px;
        }
        
        .qr-title {
          font-size: 14px;
        }
        
        .qr-subtitle {
          font-size: 12px;
        }
      }
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.wechat-bind-panel {
  animation: fadeIn 0.3s ease-out;
}

// 二维码扫描动画
@keyframes qrScan {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(7, 193, 96, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 0 10px rgba(7, 193, 96, 0.1);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(7, 193, 96, 0);
  }
}

.binding-section .qrcode-display img {
  animation: qrScan 2s infinite;
}
