.text-danger {
  color: $theme_color;
}
.seckill-price{
  margin-right: 5px;
  font-size: 25px;
  font-weight: bold;
}
.goods-list {
  display: flex;
  flex-wrap: wrap;
  width: 1200px;
  margin: 0 auto;
}
.goods-show-info {
  width: 235px;
  padding: 6px;
  margin: 10px 0px;
  margin-left: 5px;
  position: relative;
  border: 1px solid #fff;
  cursor: pointer;
  background-color: #fff;
}
.goods-show-info:hover {
  border: 1px solid #ccc;
  box-shadow: 0px 0px 15px #ccc;
}
.goods-show-price {
  margin-top: 6px;
}
.goods-show-detail {
  height: 36px;
  font-size: 12px;
  margin: 6px 0px;
  overflow:hidden;
  text-overflow: ellipsis;
  display:-webkit-box;
  -webkit-line-clamp:2;
  -webkit-box-orient:vertical;
  color:#808080;
}
.goods-show-detail :hover{
  color:#E23A3A;
}
.goods-show-num {
  font-size: 12px;
  margin-bottom: 6px;
  color: #666;
}
.goods-show-num {
  font-size: 12px;
  margin-bottom: 6px;
  color: #009688;
}
.goods-show-num span {
  color: #005aa0;
  font-weight: bold;
}
.goods-show-seller {
  font-size: 12px;
  color: $theme_color;
}
.goods-page {
  margin:10px auto ;
  text-align: right;
  width: 1200px;
}